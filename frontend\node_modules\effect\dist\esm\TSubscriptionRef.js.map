{"version": 3, "file": "TSubscriptionRef.js", "names": ["internal", "TSubscriptionRefTypeId", "get", "getAndSet", "getAndUpdate", "getAndUpdateSome", "make", "modify", "modifySome", "set", "setAndGet", "update", "updateAndGet", "updateSome", "updateSomeAndGet", "changesScoped", "changesStream", "changes", "self"], "sources": ["../../src/TSubscriptionRef.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,QAAQ,MAAM,oCAAoC;AAU9D;;;;AAIA,OAAO,MAAMC,sBAAsB,GAAkBD,QAAQ,CAACC,sBAAsB;AA6CpF;;;;AAIA,OAAO,MAAMC,GAAG,GAAiDF,QAAQ,CAACE,GAAG;AAE7E;;;;AAIA,OAAO,MAAMC,SAAS,GAWlBH,QAAQ,CAACG,SAAS;AAEtB;;;;AAIA,OAAO,MAAMC,YAAY,GAWrBJ,QAAQ,CAACI,YAAY;AAEzB;;;;AAIA,OAAO,MAAMC,gBAAgB,GAWzBL,QAAQ,CAACK,gBAAgB;AAE7B;;;;AAIA,OAAO,MAAMC,IAAI,GAAkDN,QAAQ,CAACM,IAAI;AAEhF;;;;AAIA,OAAO,MAAMC,MAAM,GAWfP,QAAQ,CAACO,MAAM;AAEnB;;;;AAIA,OAAO,MAAMC,UAAU,GAenBR,QAAQ,CAACQ,UAAU;AAEvB;;;;AAIA,OAAO,MAAMC,GAAG,GAWZT,QAAQ,CAACS,GAAG;AAEhB;;;;AAIA,OAAO,MAAMC,SAAS,GAWlBV,QAAQ,CAACU,SAAS;AAEtB;;;;AAIA,OAAO,MAAMC,MAAM,GAWfX,QAAQ,CAACW,MAAM;AAEnB;;;;AAIA,OAAO,MAAMC,YAAY,GAWrBZ,QAAQ,CAACY,YAAY;AAEzB;;;;AAIA,OAAO,MAAMC,UAAU,GAWnBb,QAAQ,CAACa,UAAU;AAEvB;;;;AAIA,OAAO,MAAMC,gBAAgB,GAWzBd,QAAQ,CAACc,gBAAgB;AAE7B;;;;AAIA,OAAO,MAAMC,aAAa,GACxBf,QAAQ,CAACe,aAAa;AAExB;;;;AAIA,OAAO,MAAMC,aAAa,GAAuDhB,QAAQ,CAACgB,aAAa;AAEvG;;;;AAIA,OAAO,MAAMC,OAAO,GAAmEC,IAAI,IAAKA,IAAI,CAACD,OAAO", "ignoreList": []}