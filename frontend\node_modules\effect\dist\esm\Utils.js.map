{"version": 3, "file": "Utils.js", "names": ["identity", "globalValue", "getBugErrorMessage", "isNullable", "isObject", "GenKindTypeId", "Symbol", "for", "isGenKind", "u", "GenKindImpl", "value", "constructor", "_F", "_R", "_", "_O", "_E", "iterator", "SingleShotGen", "self", "called", "next", "a", "done", "return", "throw", "e", "makeGenKind", "kind", "adapter", "x", "arguments", "i", "length", "defaultIncHi", "defaultIncLo", "MUL_HI", "MUL_LO", "BIT_53", "BIT_27", "PCGRandom", "_state", "seedHi", "seedLo", "incHi", "incLo", "Math", "random", "Int32Array", "_next", "add64", "getState", "setState", "state", "integer", "max", "round", "number", "Number", "MAX_SAFE_INTEGER", "hi", "lo", "oldHi", "oldLo", "mul64", "xsHi", "xsLo", "xorshifted", "rot", "rot2", "out", "aHi", "aLo", "bHi", "bLo", "c1", "c0", "imul", "YieldWrapTypeId", "YieldWrap", "yieldWrapGet", "Error", "structuralRegionState", "enabled", "tester", "undefined", "structuralRegion", "body", "current", "currentTester", "standard", "effect_internal_function", "forced", "isNotOptimizedAway", "stack", "includes", "internalCall", "genConstructor", "isGeneratorFunction"], "sources": ["../../src/Utils.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAgB;AAGrD;;;;;;;;;;;;AAaA;;;;AAIA,OAAO,MAAMC,aAAa,gBAAkBC,MAAM,CAACC,GAAG,CAAC,oBAAoB,CAAC;AAkB5E;;;;AAIA,OAAO,MAAMC,SAAS,GAAIC,CAAU,IAA4CL,QAAQ,CAACK,CAAC,CAAC,IAAIJ,aAAa,IAAII,CAAC;AAEjH;;;;AAIA,OAAM,MAAOC,WAAW;EAKXC,KAAA;EAJXC;EACE;;;EAGSD,KAA0B;IAA1B,KAAAA,KAAK,GAALA,KAAK;EACb;EAEH;;;EAGA,IAAIE,EAAEA,CAAA;IACJ,OAAOb,QAAQ;EACjB;EAEA;;;EAGA,IAAIc,EAAEA,CAAA;IACJ,OAAQC,CAAI,IAAKA,CAAC;EACpB;EAEA;;;EAGA,IAAIC,EAAEA,CAAA;IACJ,OAAQD,CAAQ,IAAQA,CAAC;EAC3B;EAEA;;;EAGA,IAAIE,EAAEA,CAAA;IACJ,OAAQF,CAAQ,IAAQA,CAAC;EAC3B;EAEA;;;EAGS,CAACV,aAAa,IAA0BA,aAAa;EAE9D;;;EAGA,CAACC,MAAM,CAACY,QAAQ,IAAC;IACf,OAAO,IAAIC,aAAa,CAA4B,IAAW,CAAC;EAClE;;AAGF;;;;AAIA,OAAM,MAAOA,aAAa;EAGHC,IAAA;EAFbC,MAAM,GAAG,KAAK;EAEtBT,YAAqBQ,IAAO;IAAP,KAAAA,IAAI,GAAJA,IAAI;EAAM;EAE/B;;;EAGAE,IAAIA,CAACC,CAAI;IACP,OAAO,IAAI,CAACF,MAAM,GACf;MACCV,KAAK,EAAEY,CAAC;MACRC,IAAI,EAAE;KACP,IACA,IAAI,CAACH,MAAM,GAAG,IAAI,EAChB;MACCV,KAAK,EAAE,IAAI,CAACS,IAAI;MAChBI,IAAI,EAAE;KACN,CAAC;EACT;EAEA;;;EAGAC,MAAMA,CAACF,CAAI;IACT,OAAQ;MACNZ,KAAK,EAAEY,CAAC;MACRC,IAAI,EAAE;KACP;EACH;EAEA;;;EAGAE,KAAKA,CAACC,CAAU;IACd,MAAMA,CAAC;EACT;EAEA;;;EAGA,CAACrB,MAAM,CAACY,QAAQ,IAAC;IACf,OAAO,IAAIC,aAAa,CAAO,IAAI,CAACC,IAAI,CAAC;EAC3C;;AAGF;;;;AAIA,OAAO,MAAMQ,WAAW,GACtBC,IAAyB,IACE,IAAInB,WAAW,CAACmB,IAAI,CAAC;AA4TlD;;;;AAIA,OAAO,MAAMC,OAAO,GAA2CA,CAAA,KAAO;EACpE,IAAIC,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACzCF,CAAC,GAAGC,SAAS,CAACC,CAAC,CAAC,CAACF,CAAC,CAAC;EACrB;EACA,OAAO,IAAIrB,WAAW,CAACqB,CAAC,CAAQ;AAClC,CAAE;AAEF,MAAMI,YAAY,GAAG,UAAU;AAC/B,MAAMC,YAAY,GAAG,UAAU;AAC/B,MAAMC,MAAM,GAAG,UAAU,KAAK,CAAC;AAC/B,MAAMC,MAAM,GAAG,UAAU,KAAK,CAAC;AAC/B,MAAMC,MAAM,GAAG,kBAAkB;AACjC,MAAMC,MAAM,GAAG,WAAW;AAc1B;;;;;;;;AAQA,OAAM,MAAOC,SAAS;EACZC,MAAM;EAoCd9B,YACE+B,MAAuB,EACvBC,MAAuB,EACvBC,KAAsB,EACtBC,KAAsB;IAEtB,IAAI3C,UAAU,CAACyC,MAAM,CAAC,IAAIzC,UAAU,CAACwC,MAAM,CAAC,EAAE;MAC5CC,MAAM,GAAIG,IAAI,CAACC,MAAM,EAAE,GAAG,UAAU,KAAM,CAAC;MAC3CL,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM,IAAIxC,UAAU,CAACyC,MAAM,CAAC,EAAE;MAC7BA,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAG,CAAC;IACZ;IACA,IAAIxC,UAAU,CAAC2C,KAAK,CAAC,IAAI3C,UAAU,CAAC0C,KAAK,CAAC,EAAE;MAC1CC,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGN,YAAY;MACnDS,KAAK,GAAG,IAAI,CAACH,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGP,YAAY;IACrD,CAAC,MAAM,IAAIhC,UAAU,CAAC2C,KAAK,CAAC,EAAE;MAC5BA,KAAK,GAAYD,KAAK;MACtBA,KAAK,GAAG,CAAC;IACX;IAEA,IAAI,CAACH,MAAM,GAAG,IAAIO,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAYJ,KAAM,KAAK,CAAC,EAAE,CAAC,CAACC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACtF,IAAI,CAACI,KAAK,EAAE;IACZC,KAAK,CACH,IAAI,CAACT,MAAM,EACX,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EACf,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EACLC,MAAO,KAAK,CAAC,EACbC,MAAO,KAAK,CAAC,CACxB;IACD,IAAI,CAACM,KAAK,EAAE;IACZ,OAAO,IAAI;EACb;EAEA;;;;;;;EAOAE,QAAQA,CAAA;IACN,OAAO,CAAC,IAAI,CAACV,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,CAAC;EAC7E;EAEA;;;;;EAKAW,QAAQA,CAACC,KAAqB;IAC5B,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAGY,KAAK,CAAC,CAAC,CAAC;IACzB,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAGY,KAAK,CAAC,CAAC,CAAC;IACzB,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAGY,KAAK,CAAC,CAAC,CAAC;IACzB,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAGY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;EAC/B;EAEA;;;;;;EAMAC,OAAOA,CAACC,GAAW;IACjB,OAAOT,IAAI,CAACU,KAAK,CAAC,IAAI,CAACC,MAAM,EAAE,GAAGC,MAAM,CAACC,gBAAgB,CAAC,GAAGJ,GAAG;EAClE;EAEA;;;;;;;EAOAE,MAAMA,CAAA;IACJ,MAAMG,EAAE,GAAG,CAAC,IAAI,CAACX,KAAK,EAAE,GAAG,UAAU,IAAI,GAAG;IAC5C,MAAMY,EAAE,GAAG,CAAC,IAAI,CAACZ,KAAK,EAAE,GAAG,UAAU,IAAI,GAAG;IAC5C,OAAO,CAACW,EAAE,GAAGrB,MAAM,GAAGsB,EAAE,IAAIvB,MAAM;EACpC;EAEA;EACQW,KAAKA,CAAA;IACX;IACA,MAAMa,KAAK,GAAG,IAAI,CAACrB,MAAM,CAAC,CAAC,CAAE,KAAK,CAAC;IACnC,MAAMsB,KAAK,GAAG,IAAI,CAACtB,MAAM,CAAC,CAAC,CAAE,KAAK,CAAC;IAEnC;IACAuB,KAAK,CAAC,IAAI,CAACvB,MAAM,EAAEqB,KAAK,EAAEC,KAAK,EAAE3B,MAAM,EAAEC,MAAM,CAAC;IAChDa,KAAK,CAAC,IAAI,CAACT,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,CAAC;IAEtF;IACA,IAAIwB,IAAI,GAAGH,KAAK,KAAK,EAAE;IACvB,IAAII,IAAI,GAAG,CAAEH,KAAK,KAAK,EAAE,GAAKD,KAAK,IAAI,EAAG,MAAM,CAAC;IACjDG,IAAI,GAAG,CAACA,IAAI,GAAGH,KAAK,MAAM,CAAC;IAC3BI,IAAI,GAAG,CAACA,IAAI,GAAGH,KAAK,MAAM,CAAC;IAC3B,MAAMI,UAAU,GAAG,CAAED,IAAI,KAAK,EAAE,GAAKD,IAAI,IAAI,CAAE,MAAM,CAAC;IACtD;IACA;IACA,MAAMG,GAAG,GAAGN,KAAK,KAAK,EAAE;IACxB,MAAMO,IAAI,GAAG,CAAE,CAACD,GAAG,KAAK,CAAC,GAAI,EAAE,MAAM,CAAC;IACtC,OAAO,CAAED,UAAU,KAAKC,GAAG,GAAKD,UAAU,IAAIE,IAAK,MAAM,CAAC;EAC5D;;AAGF,SAASL,KAAKA,CACZM,GAAe,EACfC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,GAAW;EAEX,IAAIC,EAAE,GAAI,CAACH,GAAG,KAAK,EAAE,KAAKE,GAAG,GAAG,MAAM,CAAC,KAAM,CAAC;EAC9C,IAAIE,EAAE,GAAI,CAACJ,GAAG,GAAG,MAAM,KAAKE,GAAG,KAAK,EAAE,CAAC,KAAM,CAAC;EAE9C,IAAIb,EAAE,GAAI,CAACW,GAAG,GAAG,MAAM,KAAKE,GAAG,GAAG,MAAM,CAAC,KAAM,CAAC;EAChD,IAAId,EAAE,GAAI,CAACY,GAAG,KAAK,EAAE,KAAKE,GAAG,KAAK,EAAE,CAAC,IAAI,CAACE,EAAE,KAAK,EAAE,KAAKD,EAAE,KAAK,EAAE,CAAC,CAAC,KAAM,CAAC;EAE1EC,EAAE,GAAIA,EAAE,IAAI,EAAE,KAAM,CAAC;EACrBf,EAAE,GAAIA,EAAE,GAAGe,EAAE,KAAM,CAAC;EACpB,IAAKf,EAAE,KAAK,CAAC,GAAKe,EAAE,KAAK,CAAE,EAAE;IAC3BhB,EAAE,GAAIA,EAAE,GAAG,CAAC,KAAM,CAAC;EACrB;EAEAe,EAAE,GAAIA,EAAE,IAAI,EAAE,KAAM,CAAC;EACrBd,EAAE,GAAIA,EAAE,GAAGc,EAAE,KAAM,CAAC;EACpB,IAAKd,EAAE,KAAK,CAAC,GAAKc,EAAE,KAAK,CAAE,EAAE;IAC3Bf,EAAE,GAAIA,EAAE,GAAG,CAAC,KAAM,CAAC;EACrB;EAEAA,EAAE,GAAIA,EAAE,GAAGd,IAAI,CAAC+B,IAAI,CAACL,GAAG,EAAEC,GAAG,CAAC,KAAM,CAAC;EACrCb,EAAE,GAAIA,EAAE,GAAGd,IAAI,CAAC+B,IAAI,CAACN,GAAG,EAAEG,GAAG,CAAC,KAAM,CAAC;EAErCJ,GAAG,CAAC,CAAC,CAAC,GAAGV,EAAE;EACXU,GAAG,CAAC,CAAC,CAAC,GAAGT,EAAE;AACb;AAEA;AACA,SAASX,KAAKA,CACZoB,GAAe,EACfC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,GAAW;EAEX,IAAId,EAAE,GAAIW,GAAG,GAAGE,GAAG,KAAM,CAAC;EAC1B,MAAMZ,EAAE,GAAIW,GAAG,GAAGE,GAAG,KAAM,CAAC;EAC5B,IAAKb,EAAE,KAAK,CAAC,GAAKW,GAAG,KAAK,CAAE,EAAE;IAC5BZ,EAAE,GAAIA,EAAE,GAAG,CAAC,GAAI,CAAC;EACnB;EACAU,GAAG,CAAC,CAAC,CAAC,GAAGV,EAAE;EACXU,GAAG,CAAC,CAAC,CAAC,GAAGT,EAAE;AACb;AAEA;;;AAGA,OAAO,MAAMiB,eAAe,gBAAkBzE,MAAM,CAACC,GAAG,CAAC,wBAAwB,CAAC;AAElF;;;AAGA,OAAM,MAAOyE,SAAS;EACpB;;;EAGS,CAAArE,KAAM;EACfC,YAAYD,KAAQ;IAClB,IAAI,CAAC,CAAAA,KAAM,GAAGA,KAAK;EACrB;EACA;;;EAGA,CAACoE,eAAe,IAAC;IACf,OAAO,IAAI,CAAC,CAAApE,KAAM;EACpB;;AAGF;;;AAGA,OAAM,SAAUsE,YAAYA,CAAI7D,IAAkB;EAChD,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI2D,eAAe,IAAI3D,IAAI,EAAE;IACxE,OAAOA,IAAI,CAAC2D,eAAe,CAAC,EAAE;EAChC;EACA,MAAM,IAAIG,KAAK,CAAChF,kBAAkB,CAAC,cAAc,CAAC,CAAC;AACrD;AAEA;;;;;;;AAOA,OAAO,MAAMiF,qBAAqB,gBAAGlF,WAAW,CAC9C,iCAAiC,EACjC,OAAwF;EACtFmF,OAAO,EAAE,KAAK;EACdC,MAAM,EAAEC;CACT,CAAC,CACH;AAED;;;;;;;AAOA,OAAO,MAAMC,gBAAgB,GAAGA,CAAIC,IAAa,EAAEH,MAA4C,KAAO;EACpG,MAAMI,OAAO,GAAGN,qBAAqB,CAACC,OAAO;EAC7C,MAAMM,aAAa,GAAGP,qBAAqB,CAACE,MAAM;EAClDF,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACpC,IAAIC,MAAM,EAAE;IACVF,qBAAqB,CAACE,MAAM,GAAGA,MAAM;EACvC;EACA,IAAI;IACF,OAAOG,IAAI,EAAE;EACf,CAAC,SAAS;IACRL,qBAAqB,CAACC,OAAO,GAAGK,OAAO;IACvCN,qBAAqB,CAACE,MAAM,GAAGK,aAAa;EAC9C;AACF,CAAC;AAED,MAAMC,QAAQ,GAAG;EACfC,wBAAwB,EAAMJ,IAAa,IAAI;IAC7C,OAAOA,IAAI,EAAE;EACf;CACD;AAED,MAAMK,MAAM,GAAG;EACbD,wBAAwB,EAAMJ,IAAa,IAAI;IAC7C,IAAI;MACF,OAAOA,IAAI,EAAE;IACf,CAAC,SAAS;MACR;IAAA;EAEJ;CACD;AAED,MAAMM,kBAAkB,GACtB,aAAAH,QAAQ,CAACC,wBAAwB,CAAC,MAAM,IAAIV,KAAK,EAAE,CAACa,KAAK,CAAC,EAAEC,QAAQ,CAAC,0BAA0B,CAAC,KAAK,IAAI;AAE3G;;;;;AAKA,OAAO,MAAMC,YAAY,GAAGH,kBAAkB,GAAGH,QAAQ,CAACC,wBAAwB,GAAGC,MAAM,CAACD,wBAAwB;AAEpH,MAAMM,cAAc,GAAI,aAAS,CAAI,CAAC,CAAEtF,WAAW;AAEnD;;;AAGA,OAAO,MAAMuF,mBAAmB,GAAI1F,CAAU,IAC5CL,QAAQ,CAACK,CAAC,CAAC,IAAIA,CAAC,CAACG,WAAW,KAAKsF,cAAc", "ignoreList": []}