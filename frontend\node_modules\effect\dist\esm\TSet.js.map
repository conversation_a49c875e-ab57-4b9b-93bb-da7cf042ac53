{"version": 3, "file": "TSet.js", "names": ["internal", "TSetTypeId", "add", "difference", "empty", "for<PERSON>ach", "fromIterable", "has", "intersection", "isEmpty", "make", "reduce", "reduceSTM", "remove", "removeAll", "removeIf", "retainIf", "size", "<PERSON><PERSON><PERSON><PERSON>", "takeFirstSTM", "takeSome", "takeSomeSTM", "toChunk", "toHashSet", "toArray", "toReadonlySet", "transform", "transformSTM", "union"], "sources": ["../../src/TSet.ts"], "sourcesContent": [null], "mappings": "AAKA,OAAO,KAAKA,QAAQ,MAAM,wBAAwB;AAOlD;;;;AAIA,OAAO,MAAMC,UAAU,GAAkBD,QAAQ,CAACC,UAAU;AAuC5D;;;;;;AAMA,OAAO,MAAMC,GAAG,GAeZF,QAAQ,CAACE,GAAG;AAEhB;;;;;;;AAOA,OAAO,MAAMC,UAAU,GAiBnBH,QAAQ,CAACG,UAAU;AAEvB;;;;;;AAMA,OAAO,MAAMC,KAAK,GAA8BJ,QAAQ,CAACI,KAAK;AAE9D;;;;;;AAMA,OAAO,MAAMC,OAAO,GAehBL,QAAQ,CAACK,OAAO;AAEpB;;;;;;AAMA,OAAO,MAAMC,YAAY,GAAmDN,QAAQ,CAACM,YAAY;AAEjG;;;;;;AAMA,OAAO,MAAMC,GAAG,GAeZP,QAAQ,CAACO,GAAG;AAEhB;;;;;;;AAOA,OAAO,MAAMC,YAAY,GAiBrBR,QAAQ,CAACQ,YAAY;AAEzB;;;;;;AAMA,OAAO,MAAMC,OAAO,GAA2CT,QAAQ,CAACS,OAAO;AAE/E;;;;;;AAMA,OAAO,MAAMC,IAAI,GAEsBV,QAAQ,CAACU,IAAI;AAEpD;;;;;;AAMA,OAAO,MAAMC,MAAM,GAefX,QAAQ,CAACW,MAAM;AAEnB;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelBZ,QAAQ,CAACY,SAAS;AAEtB;;;;;;AAMA,OAAO,MAAMC,MAAM,GAefb,QAAQ,CAACa,MAAM;AAEnB;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelBd,QAAQ,CAACc,SAAS;AAEtB;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAuDjBf,QAAQ,CAACe,QAAQ;AAErB;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAuDjBhB,QAAQ,CAACgB,QAAQ;AAErB;;;;;;AAMA,OAAO,MAAMC,IAAI,GAA0CjB,QAAQ,CAACiB,IAAI;AAExE;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelBlB,QAAQ,CAACkB,SAAS;AAEtB;;;;;;AAMA,OAAO,MAAMC,YAAY,GAerBnB,QAAQ,CAACmB,YAAY;AAEzB;;;;;;AAMA,OAAO,MAAMC,QAAQ,GAejBpB,QAAQ,CAACoB,QAAQ;AAErB;;;;;;AAMA,OAAO,MAAMC,WAAW,GAepBrB,QAAQ,CAACqB,WAAW;AAExB;;;;;;AAMA,OAAO,MAAMC,OAAO,GAAkDtB,QAAQ,CAACsB,OAAO;AAEtF;;;;;;AAMA,OAAO,MAAMC,SAAS,GAAsDvB,QAAQ,CAACuB,SAAS;AAE9F;;;;;;AAMA,OAAO,MAAMC,OAAO,GAA4CxB,QAAQ,CAACwB,OAAO;AAEhF;;;;;;AAMA,OAAO,MAAMC,aAAa,GAAkDzB,QAAQ,CAACyB,aAAa;AAElG;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelB1B,QAAQ,CAAC0B,SAAS;AAEtB;;;;;;AAMA,OAAO,MAAMC,YAAY,GAerB3B,QAAQ,CAAC2B,YAAY;AAEzB;;;;;;;AAOA,OAAO,MAAMC,KAAK,GAiBd5B,QAAQ,CAAC4B,KAAK", "ignoreList": []}