{"version": 3, "file": "tSubscriptionRef.js", "names": ["Effect", "dual", "pipe", "Option", "pipeArguments", "STM", "TPubSub", "TQueue", "TRef", "stream", "tDequeueVariance", "tRefV<PERSON>ce", "TSubscriptionRefSymbolKey", "TSubscriptionRefTypeId", "Symbol", "for", "TSubscriptionRefVariance", "_A", "_", "TDequeueMerge", "first", "second", "TDequeueTypeId", "constructor", "peek", "gen", "peekOption", "_tag", "value", "retry", "none", "take", "isEmpty", "takeAll", "takeUpTo", "max", "length", "capacity", "size", "isFull", "shutdown", "isShutdown", "await<PERSON><PERSON><PERSON>down", "TSubscriptionRefImpl", "ref", "pubsub", "TRefTypeId", "todos", "versioned", "arguments", "changes", "unbounded", "offer", "get", "subscribe", "modify", "f", "map", "flatMap", "b", "a", "set", "as", "zipLeft", "publish", "make", "all", "self", "getAndSet", "getAndUpdate", "getAndUpdateSome", "match", "onNone", "onSome", "setAndGet", "modifySome", "fallback", "update", "updateAndGet", "updateSome", "updateSomeAndGet", "changesScoped", "acquireRelease", "changesStream", "unwrap", "fromTQueue"], "sources": ["../../../../src/internal/stm/tSubscriptionRef.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,MAAM,MAAM,iBAAiB;AACzC,SAASC,IAAI,EAAEC,IAAI,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAO,KAAKC,GAAG,MAAM,cAAc;AACnC,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAC3C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,IAAI,MAAM,eAAe;AAErC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,YAAY,QAAQ,WAAW;AAExC;AACA,MAAMC,yBAAyB,GAAG,yBAAyB;AAE3D;AACA,OAAO,MAAMC,sBAAsB,gBAA4CC,MAAM,CAACC,GAAG,CACvFH,yBAAyB,CACiB;AAE5C,MAAMI,wBAAwB,GAAG;EAC/B;EACAC,EAAE,EAAGC,CAAM,IAAKA;CACjB;AAED,MAAMC,aAAa;EAINC,KAAA;EACAC,MAAA;EAJX,CAACd,MAAM,CAACe,cAAc,IAAIZ,gBAAgB;EAE1Ca,YACWH,KAAyB,EACzBC,MAA0B;IAD1B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;EACd;EAEHG,IAAI,gBAAenB,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IACxC,MAAML,KAAK,GAAG,OAAO,IAAI,CAACM,UAAU;IACpC,IAAIN,KAAK,CAACO,IAAI,KAAK,MAAM,EAAE;MACzB,OAAOP,KAAK,CAACQ,KAAK;IACpB;IACA,OAAO,OAAOvB,GAAG,CAACwB,KAAK;EACzB,CAAC,CAAC;EAEFH,UAAU,gBAA8BrB,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IAC7D,MAAML,KAAK,GAAG,OAAO,IAAI,CAACA,KAAK,CAACM,UAAU;IAC1C,IAAIN,KAAK,CAACO,IAAI,KAAK,MAAM,EAAE;MACzB,OAAOP,KAAK;IACd;IACA,MAAMC,MAAM,GAAG,OAAO,IAAI,CAACA,MAAM,CAACK,UAAU;IAC5C,IAAIL,MAAM,CAACM,IAAI,KAAK,MAAM,EAAE;MAC1B,OAAON,MAAM;IACf;IACA,OAAOlB,MAAM,CAAC2B,IAAI,EAAE;EACtB,CAAC,CAAC;EAEFC,IAAI,gBAAe1B,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IACxC,IAAI,EAAE,OAAO,IAAI,CAACL,KAAK,CAACY,OAAO,CAAC,EAAE;MAChC,OAAO,OAAO,IAAI,CAACZ,KAAK,CAACW,IAAI;IAC/B;IACA,IAAI,EAAE,OAAO,IAAI,CAACV,MAAM,CAACW,OAAO,CAAC,EAAE;MACjC,OAAO,OAAO,IAAI,CAACX,MAAM,CAACU,IAAI;IAChC;IACA,OAAO,OAAO1B,GAAG,CAACwB,KAAK;EACzB,CAAC,CAAC;EAEFI,OAAO,gBAAsB5B,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IAClD,OAAO,CAAC,IAAG,OAAO,IAAI,CAACL,KAAK,CAACa,OAAO,GAAE,IAAG,OAAO,IAAI,CAACZ,MAAM,CAACY,OAAO,EAAC;EACtE,CAAC,CAAC;EAEFC,QAAQA,CAACC,GAAW;IAClB,OAAO9B,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;MAC5B,MAAML,KAAK,GAAG,OAAO,IAAI,CAACA,KAAK,CAACc,QAAQ,CAACC,GAAG,CAAC;MAC7C,IAAIf,KAAK,CAACgB,MAAM,IAAID,GAAG,EAAE;QACvB,OAAOf,KAAK;MACd;MACA,OAAO,CAAC,GAAGA,KAAK,EAAE,IAAG,OAAO,IAAI,CAACC,MAAM,CAACa,QAAQ,CAACC,GAAG,GAAGf,KAAK,CAACgB,MAAM,CAAC,EAAC;IACvE,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ,EAAE,GAAG,IAAI,CAAChB,MAAM,CAACgB,QAAQ,EAAE;EACvD;EAEAC,IAAI,gBAAoBjC,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IAC7C,OAAO,CAAC,OAAO,IAAI,CAACL,KAAK,CAACkB,IAAI,KAAK,OAAO,IAAI,CAACjB,MAAM,CAACiB,IAAI,CAAC;EAC7D,CAAC,CAAC;EAEFC,MAAM,gBAAqBlC,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IAChD,OAAO,CAAC,OAAO,IAAI,CAACL,KAAK,CAACmB,MAAM,MAAM,OAAO,IAAI,CAAClB,MAAM,CAACkB,MAAM,CAAC;EAClE,CAAC,CAAC;EAEFP,OAAO,gBAAqB3B,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IACjD,OAAO,CAAC,OAAO,IAAI,CAACL,KAAK,CAACY,OAAO,MAAM,OAAO,IAAI,CAACX,MAAM,CAACW,OAAO,CAAC;EACpE,CAAC,CAAC;EAEFQ,QAAQ,gBAAkBnC,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IAC/C,OAAO,IAAI,CAACL,KAAK,CAACoB,QAAQ;IAC1B,OAAO,IAAI,CAACnB,MAAM,CAACmB,QAAQ;EAC7B,CAAC,CAAC;EAEFC,UAAU,gBAAqBpC,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IACpD,OAAO,CAAC,OAAO,IAAI,CAACL,KAAK,CAACqB,UAAU,MAAM,OAAO,IAAI,CAACpB,MAAM,CAACoB,UAAU,CAAC;EAC1E,CAAC,CAAC;EAEFC,aAAa,gBAAkBrC,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;IACpD,OAAO,IAAI,CAACL,KAAK,CAACsB,aAAa;IAC/B,OAAO,IAAI,CAACrB,MAAM,CAACqB,aAAa;EAClC,CAAC,CAAC;;AAGJ;AACA,MAAMC,oBAAoB;EAKbC,GAAA;EACAC,MAAA;EALF,CAAChC,sBAAsB,IAAIG,wBAAwB;EACnD,CAACR,IAAI,CAACsC,UAAU,IAAInC,YAAY;EAEzCY,YACWqB,GAAiB,EACjBC,MAA0B;IAD1B,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;EACd;EAEH,IAAIE,KAAKA,CAAA;IACP,OAAO,IAAI,CAACH,GAAG,CAACG,KAAK;EACvB;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACJ,GAAG,CAACI,SAAS;EAC3B;EAEA9C,IAAIA,CAAA;IACF,OAAOE,aAAa,CAAC,IAAI,EAAE6C,SAAS,CAAC;EACvC;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAO7C,GAAG,CAACoB,GAAG,CAAC,IAAI,EAAE,aAAS;MAC5B,MAAML,KAAK,GAAG,OAAOb,MAAM,CAAC4C,SAAS,EAAK;MAC1C,OAAO5C,MAAM,CAAC6C,KAAK,CAAChC,KAAK,EAAE,OAAOZ,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAACT,GAAG,CAAC,CAAC;MACrD,OAAO,IAAIzB,aAAa,CAACC,KAAK,EAAE,OAAOd,OAAO,CAACgD,SAAS,CAAC,IAAI,CAACT,MAAM,CAAC,CAAC;IACxE,CAAC,CAAC;EACJ;EAEAU,MAAMA,CAAIC,CAA4B;IACpC,OAAOtD,IAAI,CACTM,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAACT,GAAG,CAAC,EAClBvC,GAAG,CAACoD,GAAG,CAACD,CAAC,CAAC,EACVnD,GAAG,CAACqD,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KACjB1D,IAAI,CACFM,IAAI,CAACqD,GAAG,CAAC,IAAI,CAACjB,GAAG,EAAEgB,CAAC,CAAC,EACrBvD,GAAG,CAACyD,EAAE,CAACH,CAAC,CAAC,EACTtD,GAAG,CAAC0D,OAAO,CAACzD,OAAO,CAAC0D,OAAO,CAAC,IAAI,CAACnB,MAAM,EAAEe,CAAC,CAAC,CAAC,CAC7C,CACF,CACF;EACH;;AAGF;AACA,OAAO,MAAMK,IAAI,GAAOrC,KAAQ,IAC9B1B,IAAI,CACFG,GAAG,CAAC6D,GAAG,CAAC,CACN5D,OAAO,CAAC6C,SAAS,EAAK,EACtB3C,IAAI,CAACyD,IAAI,CAACrC,KAAK,CAAC,CACjB,CAAC,EACFvB,GAAG,CAACoD,GAAG,CAAC,CAAC,CAACZ,MAAM,EAAED,GAAG,CAAC,KAAK,IAAID,oBAAoB,CAACC,GAAG,EAAEC,MAAM,CAAC,CAAC,CAClE;AAEH;AACA,OAAO,MAAMQ,GAAG,GAAOc,IAA0C,IAAK3D,IAAI,CAAC6C,GAAG,CAACc,IAAI,CAACvB,GAAG,CAAC;AAExF;AACA,OAAO,MAAMiB,GAAG,gBAAG5D,IAAI,CAIrB,CAAC,EACD,CAAIkE,IAA0C,EAAEvC,KAAQ,KACtDuC,IAAI,CAACZ,MAAM,CAAC,MAAiB,CAAC,KAAK,CAAC,EAAE3B,KAAK,CAAC,CAAC,CAChD;AAED;AACA,OAAO,MAAMwC,SAAS,gBAAGnE,IAAI,CAG3B,CAAC,EAAE,CAACkE,IAAI,EAAEvC,KAAK,KAAKuC,IAAI,CAACZ,MAAM,CAAEK,CAAC,IAAK,CAACA,CAAC,EAAEhC,KAAK,CAAC,CAAC,CAAC;AAErD;AACA,OAAO,MAAMyC,YAAY,gBAAGpE,IAAI,CAG9B,CAAC,EAAE,CAACkE,IAAI,EAAEX,CAAC,KAAKW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IAAK,CAACA,CAAC,EAAEJ,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhD;AACA,OAAO,MAAMU,gBAAgB,gBAAGrE,IAAI,CAGlC,CAAC,EAAE,CAACkE,IAAI,EAAEX,CAAC,KACXW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IACZzD,MAAM,CAACoE,KAAK,CAACf,CAAC,CAACI,CAAC,CAAC,EAAE;EACjBY,MAAM,EAAEA,CAAA,KAAM,CAACZ,CAAC,EAAEA,CAAC,CAAC;EACpBa,MAAM,EAAGd,CAAC,IAAK,CAACC,CAAC,EAAED,CAAC;CACrB,CAAC,CACH,CAAC;AAEJ;AACA,OAAO,MAAMe,SAAS,gBAAGzE,IAAI,CAG3B,CAAC,EAAE,CAACkE,IAAI,EAAEvC,KAAK,KAAKuC,IAAI,CAACZ,MAAM,CAAC,MAAM,CAAC3B,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC;AAExD;AACA,OAAO,MAAM2B,MAAM,gBAAGtD,IAAI,CAGxB,CAAC,EAAE,CAACkE,IAAI,EAAEX,CAAC,KAAKW,IAAI,CAACZ,MAAM,CAACC,CAAC,CAAC,CAAC;AAEjC;AACA,OAAO,MAAMmB,UAAU,gBAAG1E,IAAI,CAU5B,CAAC,EAAE,CAACkE,IAAI,EAAES,QAAQ,EAAEpB,CAAC,KACrBW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IACZzD,MAAM,CAACoE,KAAK,CAACf,CAAC,CAACI,CAAC,CAAC,EAAE;EACjBY,MAAM,EAAEA,CAAA,KAAM,CAACI,QAAQ,EAAEhB,CAAC,CAAC;EAC3Ba,MAAM,EAAGd,CAAC,IAAKA;CAChB,CAAC,CACH,CAAC;AAEJ;AACA,OAAO,MAAMkB,MAAM,gBAAG5E,IAAI,CAGxB,CAAC,EAAE,CAACkE,IAAI,EAAEX,CAAC,KAAKW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IAAK,CAAC,KAAK,CAAC,EAAEJ,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;AAErD;AACA,OAAO,MAAMkB,YAAY,gBAAG7E,IAAI,CAG9B,CAAC,EAAE,CAACkE,IAAI,EAAEX,CAAC,KACXW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IAAI;EAChB,MAAMD,CAAC,GAAGH,CAAC,CAACI,CAAC,CAAC;EACd,OAAO,CAACD,CAAC,EAAEA,CAAC,CAAC;AACf,CAAC,CAAC,CAAC;AAEL;AACA,OAAO,MAAMoB,UAAU,gBAAG9E,IAAI,CAI5B,CAAC,EACD,CAACkE,IAAI,EAAEX,CAAC,KACNW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IAAK,CACjB,KAAK,CAAC,EACNzD,MAAM,CAACoE,KAAK,CAACf,CAAC,CAACI,CAAC,CAAC,EAAE;EACjBY,MAAM,EAAEA,CAAA,KAAMZ,CAAC;EACfa,MAAM,EAAGd,CAAC,IAAKA;CAChB,CAAC,CACH,CAAC,CACL;AAED;AACA,OAAO,MAAMqB,gBAAgB,gBAAG/E,IAAI,CAIlC,CAAC,EACD,CAACkE,IAAI,EAAEX,CAAC,KACNW,IAAI,CAACZ,MAAM,CAAEK,CAAC,IACZzD,MAAM,CAACoE,KAAK,CAACf,CAAC,CAACI,CAAC,CAAC,EAAE;EACjBY,MAAM,EAAEA,CAAA,KAAM,CAACZ,CAAC,EAAEA,CAAC,CAAC;EACpBa,MAAM,EAAGd,CAAC,IAAK,CAACA,CAAC,EAAEA,CAAC;CACrB,CAAC,CACH,CACJ;AAED;AACA,OAAO,MAAMsB,aAAa,GAAOd,IAA0C,IACzEnE,MAAM,CAACkF,cAAc,CAACf,IAAI,CAACjB,OAAO,EAAE3C,MAAM,CAACiC,QAAQ,CAAC;AAEtD;AACA,OAAO,MAAM2C,aAAa,GAAOhB,IAA0C,IACzE1D,MAAM,CAAC2E,MAAM,CAACpF,MAAM,CAACyD,GAAG,CAACU,IAAI,CAACjB,OAAO,EAAEzC,MAAM,CAAC4E,UAAU,CAAC,CAAC", "ignoreList": []}