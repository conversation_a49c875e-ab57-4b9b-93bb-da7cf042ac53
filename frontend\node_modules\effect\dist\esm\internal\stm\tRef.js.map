{"version": 3, "file": "tRef.js", "names": ["dual", "Option", "pipeArguments", "core", "Entry", "Versioned", "TRefSymbolKey", "TRefTypeId", "Symbol", "for", "tRefV<PERSON>ce", "_A", "_", "TRefImpl", "todos", "versioned", "constructor", "value", "Map", "modify", "f", "effect", "journal", "entry", "getOrMakeEntry", "retValue", "newValue", "unsafeGet", "unsafeSet", "pipe", "arguments", "make", "ref", "set", "get", "self", "a", "getAndSet", "getAndUpdate", "getAndUpdateSome", "match", "onNone", "onSome", "b", "setAndGet", "modifySome", "fallback", "update", "updateAndGet", "updateSome", "updateSomeAndGet", "has", "undefined"], "sources": ["../../../../src/internal/stm/tRef.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,IAAI,QAAQ,mBAAmB;AACxC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AAEzC,SAASC,aAAa,QAAQ,mBAAmB;AAGjD,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,YAAY;AAGnC,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAE3C;AACA,MAAMC,aAAa,GAAG,aAAa;AAEnC;AACA,OAAO,MAAMC,UAAU,gBAAoBC,MAAM,CAACC,GAAG,CACnDH,aAAa,CACK;AAEpB,OAAO,MAAMI,YAAY,GAAG;EAC1B;EACAC,EAAE,EAAGC,CAAM,IAAKA;CACjB;AAED;AACA,OAAM,MAAOC,QAAQ;EACV,CAACN,UAAU,IAAIG,YAAY;EACpC;EACAI,KAAK;EACL;EACAC,SAAS;EACTC,YAAYC,KAAQ;IAClB,IAAI,CAACF,SAAS,GAAG,IAAIV,SAAS,CAACA,SAAS,CAACY,KAAK,CAAC;IAC/C,IAAI,CAACH,KAAK,GAAG,IAAII,GAAG,EAAE;EACxB;EACAC,MAAMA,CAAIC,CAA4B;IACpC,OAAOjB,IAAI,CAACkB,MAAM,CAAYC,OAAO,IAAI;MACvC,MAAMC,KAAK,GAAGC,cAAc,CAAC,IAAI,EAAEF,OAAO,CAAC;MAC3C,MAAM,CAACG,QAAQ,EAAEC,QAAQ,CAAC,GAAGN,CAAC,CAAChB,KAAK,CAACuB,SAAS,CAACJ,KAAK,CAAM,CAAC;MAC3DnB,KAAK,CAACwB,SAAS,CAACL,KAAK,EAAEG,QAAQ,CAAC;MAChC,OAAOD,QAAQ;IACjB,CAAC,CAAC;EACJ;EACAI,IAAIA,CAAA;IACF,OAAO3B,aAAa,CAAC,IAAI,EAAE4B,SAAS,CAAC;EACvC;;AAGF;AACA,OAAO,MAAMC,IAAI,GAAOd,KAAQ,IAC9Bd,IAAI,CAACkB,MAAM,CAAuBC,OAAO,IAAI;EAC3C,MAAMU,GAAG,GAAG,IAAInB,QAAQ,CAACI,KAAK,CAAC;EAC/BK,OAAO,CAACW,GAAG,CAACD,GAAG,EAAE5B,KAAK,CAAC2B,IAAI,CAACC,GAAG,EAAE,IAAI,CAAC,CAAC;EACvC,OAAOA,GAAG;AACZ,CAAC,CAAC;AAEJ;AACA,OAAO,MAAME,GAAG,GAAOC,IAAkB,IAAKA,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK,CAACA,CAAC,EAAEA,CAAC,CAAC,CAAC;AAExE;AACA,OAAO,MAAMH,GAAG,gBAAGjC,IAAI,CAIrB,CAAC,EACD,CAAImC,IAAkB,EAAElB,KAAQ,KAAoBkB,IAAI,CAAChB,MAAM,CAAC,MAAiB,CAAC,KAAK,CAAC,EAAEF,KAAK,CAAC,CAAC,CAClG;AAED;AACA,OAAO,MAAMoB,SAAS,gBAAGrC,IAAI,CAG3B,CAAC,EAAE,CAACmC,IAAI,EAAElB,KAAK,KAAKkB,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK,CAACA,CAAC,EAAEnB,KAAK,CAAC,CAAC,CAAC;AAErD;AACA,OAAO,MAAMqB,YAAY,gBAAGtC,IAAI,CAG9B,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,KAAKe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK,CAACA,CAAC,EAAEhB,CAAC,CAACgB,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhD;AACA,OAAO,MAAMG,gBAAgB,gBAAGvC,IAAI,CAGlC,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,KACXe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IACZnC,MAAM,CAACuC,KAAK,CAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;EACjBK,MAAM,EAAEA,CAAA,KAAM,CAACL,CAAC,EAAEA,CAAC,CAAC;EACpBM,MAAM,EAAGC,CAAC,IAAK,CAACP,CAAC,EAAEO,CAAC;CACrB,CAAC,CACH,CAAC;AAEJ;AACA,OAAO,MAAMC,SAAS,gBAAG5C,IAAI,CAG3B,CAAC,EAAE,CAACmC,IAAI,EAAElB,KAAK,KAAKkB,IAAI,CAAChB,MAAM,CAAC,MAAM,CAACF,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC;AAExD;AACA,OAAO,MAAME,MAAM,gBAAGnB,IAAI,CAGxB,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,KAAKe,IAAI,CAAChB,MAAM,CAACC,CAAC,CAAC,CAAC;AAEjC;AACA,OAAO,MAAMyB,UAAU,gBAAG7C,IAAI,CAG5B,CAAC,EAAE,CAACmC,IAAI,EAAEW,QAAQ,EAAE1B,CAAC,KACrBe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IACZnC,MAAM,CAACuC,KAAK,CAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;EACjBK,MAAM,EAAEA,CAAA,KAAM,CAACK,QAAQ,EAAEV,CAAC,CAAC;EAC3BM,MAAM,EAAGC,CAAC,IAAKA;CAChB,CAAC,CACH,CAAC;AAEJ;AACA,OAAO,MAAMI,MAAM,gBAAG/C,IAAI,CAGxB,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,KAAKe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK,CAAC,KAAK,CAAC,EAAEhB,CAAC,CAACgB,CAAC,CAAC,CAAC,CAAC,CAAC;AAErD;AACA,OAAO,MAAMY,YAAY,gBAAGhD,IAAI,CAG9B,CAAC,EAAE,CAACmC,IAAI,EAAEf,CAAC,KACXe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAI;EAChB,MAAMO,CAAC,GAAGvB,CAAC,CAACgB,CAAC,CAAC;EACd,OAAO,CAACO,CAAC,EAAEA,CAAC,CAAC;AACf,CAAC,CAAC,CAAC;AAEL;AACA,OAAO,MAAMM,UAAU,gBAAGjD,IAAI,CAI5B,CAAC,EACD,CAACmC,IAAI,EAAEf,CAAC,KACNe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IAAK,CACjB,KAAK,CAAC,EACNnC,MAAM,CAACuC,KAAK,CAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;EACjBK,MAAM,EAAEA,CAAA,KAAML,CAAC;EACfM,MAAM,EAAGC,CAAC,IAAKA;CAChB,CAAC,CACH,CAAC,CACL;AAED;AACA,OAAO,MAAMO,gBAAgB,gBAAGlD,IAAI,CAIlC,CAAC,EACD,CAACmC,IAAI,EAAEf,CAAC,KACNe,IAAI,CAAChB,MAAM,CAAEiB,CAAC,IACZnC,MAAM,CAACuC,KAAK,CAACpB,CAAC,CAACgB,CAAC,CAAC,EAAE;EACjBK,MAAM,EAAEA,CAAA,KAAM,CAACL,CAAC,EAAEA,CAAC,CAAC;EACpBM,MAAM,EAAGC,CAAC,IAAK,CAACA,CAAC,EAAEA,CAAC;CACrB,CAAC,CACH,CACJ;AAED;AACA,MAAMnB,cAAc,GAAGA,CAAIW,IAAkB,EAAEb,OAAwB,KAAiB;EACtF,IAAIA,OAAO,CAAC6B,GAAG,CAAChB,IAAI,CAAC,EAAE;IACrB,OAAOb,OAAO,CAACY,GAAG,CAACC,IAAI,CAAE;EAC3B;EACA,MAAMZ,KAAK,GAAGnB,KAAK,CAAC2B,IAAI,CAACI,IAAI,EAAE,KAAK,CAAC;EACrCb,OAAO,CAACW,GAAG,CAACE,IAAI,EAAEZ,KAAK,CAAC;EACxB,OAAOA,KAAK;AACd,CAAC;AAED;AACA,OAAO,MAAMI,SAAS,gBAGlB3B,IAAI,CAGN,CAAC,EAAE,CAAImC,IAAkB,EAAEb,OAAwB,KAAKlB,KAAK,CAACuB,SAAS,CAACH,cAAc,CAACW,IAAI,EAAEb,OAAO,CAAC,CAAM,CAAC;AAE9G;AACA,OAAO,MAAMM,SAAS,gBAGlB5B,IAAI,CAGN,CAAC,EAAE,CAACmC,IAAI,EAAElB,KAAK,EAAEK,OAAO,KAAI;EAC5B,MAAMC,KAAK,GAAGC,cAAc,CAACW,IAAI,EAAEb,OAAO,CAAC;EAC3ClB,KAAK,CAACwB,SAAS,CAACL,KAAK,EAAEN,KAAK,CAAC;EAC7B,OAAOmC,SAAS;AAClB,CAAC,CAAC", "ignoreList": []}