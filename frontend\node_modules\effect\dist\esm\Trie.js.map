{"version": 3, "file": "Trie.js", "names": ["TR", "TypeId", "TrieTypeId", "empty", "fromIterable", "make", "insert", "keys", "values", "entries", "toEntries", "self", "Array", "from", "keysWithPrefix", "valuesWithPrefix", "entriesWithPrefix", "toEntriesWithPrefix", "longestPrefixOf", "size", "get", "has", "isEmpty", "unsafeGet", "remove", "reduce", "map", "filter", "filterMap", "compact", "for<PERSON>ach", "modify", "remove<PERSON>any", "insertMany"], "sources": ["../../src/Trie.ts"], "sourcesContent": [null], "mappings": "AAmBA,OAAO,KAAKA,EAAE,MAAM,oBAAoB;AAKxC,MAAMC,MAAM,GAAkBD,EAAE,CAACE,UAAoB;AAkBrD;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,KAAK,GAA6BH,EAAE,CAACG,KAAK;AAEvD;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMC,YAAY,GAA4DJ,EAAE,CAACI,YAAY;AAEpG;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,IAAI,GAEwDL,EAAE,CAACK,IAAI;AAEhF;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,OAAO,MAAMC,MAAM,GAmDfN,EAAE,CAACM,MAAM;AAEb;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,IAAI,GAAmDP,EAAE,CAACO,IAAI;AAE3E;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,MAAM,GAA8CR,EAAE,CAACQ,MAAM;AAE1E;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAO,MAAMC,OAAO,GAAwDT,EAAE,CAACS,OAAO;AAEtF;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAO,MAAMC,SAAS,GAAOC,IAAa,IAAyBC,KAAK,CAACC,IAAI,CAACJ,OAAO,CAACE,IAAI,CAAC,CAAC;AAE5F;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMG,cAAc,GAiDvBd,EAAE,CAACc,cAAc;AAErB;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMC,gBAAgB,GAqDzBf,EAAE,CAACe,gBAAgB;AAEvB;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,iBAAiB,GAiD1BhB,EAAE,CAACgB,iBAAiB;AAExB;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,mBAAmB,GAiD5BjB,EAAE,CAACiB,mBAAmB;AAE1B;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,OAAO,MAAMC,eAAe,GAmDxBlB,EAAE,CAACkB,eAAe;AAEtB;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMC,IAAI,GAAiCnB,EAAE,CAACmB,IAAI;AAEzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAO,MAAMC,GAAG,GA2DZpB,EAAE,CAACoB,GAAG;AAEV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAO,MAAMC,GAAG,GA2DZrB,EAAE,CAACqB,GAAG;AAEV;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,OAAO,GAAkCtB,EAAE,CAACsB,OAAO;AAEhE;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAO,MAAMC,SAAS,GA+ClBvB,EAAE,CAACuB,SAAS;AAEhB;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,OAAO,MAAMC,MAAM,GAuDfxB,EAAE,CAACwB,MAAM;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,OAAO,MAAMC,MAAM,GA6EfzB,EAAE,CAACyB,MAAM;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,OAAO,MAAMC,GAAG,GAqEZ1B,EAAE,CAAC0B,GAAG;AAEV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,OAAO,MAAMC,MAAM,GA6Hf3B,EAAE,CAAC2B,MAAM;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,OAAO,MAAMC,SAAS,GAuElB5B,EAAE,CAAC4B,SAAS;AAEhB;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMC,OAAO,GAA0C7B,EAAE,CAAC6B,OAAO;AAExE;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMC,OAAO,GAqDhB9B,EAAE,CAAC8B,OAAO;AAEd;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAO,MAAMC,MAAM,GA+Cf/B,EAAE,CAAC+B,MAAM;AAEb;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,UAAU,GAiDnBhC,EAAE,CAACgC,UAAU;AAEjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,OAAO,MAAMC,UAAU,GA+DnBjC,EAAE,CAACiC,UAAU", "ignoreList": []}