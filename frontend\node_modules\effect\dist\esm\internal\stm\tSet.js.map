{"version": 3, "file": "tSet.js", "names": ["RA", "Chunk", "dual", "pipe", "HashSet", "hasProperty", "STM", "core", "tMap", "TSetSymbolKey", "TSetTypeId", "Symbol", "for", "tSetVariance", "_A", "_", "TSetImpl", "constructor", "isTSet", "u", "add", "self", "value", "set", "difference", "other", "flatMap", "toHashSet", "values", "removeIf", "has", "discard", "empty", "fromIterable", "for<PERSON>ach", "f", "reduceSTM", "iterable", "map", "Array", "from", "a", "intersection", "retainIf", "isEmpty", "make", "elements", "reduce", "zero", "acc", "key", "remove", "removeAll", "args", "predicate", "options", "entry", "size", "toChunk", "chunk", "length", "<PERSON><PERSON><PERSON><PERSON>", "pf", "takeFirstSTM", "takeSome", "takeSomeSTM", "keys", "unsafeFromArray", "toArray", "toReadonlySet", "Set", "transform", "transformSTM", "union"], "sources": ["../../../../src/internal/stm/tSet.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,gBAAgB;AACpC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,SAASC,IAAI,EAAEC,IAAI,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,OAAO,MAAM,kBAAkB;AAE3C,SAASC,WAAW,QAAwB,oBAAoB;AAChE,OAAO,KAAKC,GAAG,MAAM,cAAc;AAGnC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,IAAI,MAAM,WAAW;AAEjC;AACA,MAAMC,aAAa,GAAG,aAAa;AAEnC;AACA,OAAO,MAAMC,UAAU,gBAAoBC,MAAM,CAACC,GAAG,CACnDH,aAAa,CACK;AAEpB,MAAMI,YAAY,GAAG;EACnB;EACAC,EAAE,EAAGC,CAAM,IAAKA;CACjB;AAED;AACA,MAAMC,QAAQ;EAESR,IAAA;EADZ,CAACE,UAAU,IAAIG,YAAY;EACpCI,YAAqBT,IAAwB;IAAxB,KAAAA,IAAI,GAAJA,IAAI;EAAuB;;AAGlD,MAAMU,MAAM,GAAIC,CAAU,IAAKd,WAAW,CAACc,CAAC,EAAET,UAAU,CAAC;AAEzD;AACA,OAAO,MAAMU,GAAG,gBAAGlB,IAAI,CAGrB,CAAC,EAAE,CAACmB,IAAI,EAAEC,KAAK,KAAKd,IAAI,CAACe,GAAG,CAACF,IAAI,CAACb,IAAI,EAAEc,KAAK,EAAE,KAAK,CAAS,CAAC,CAAC;AAEjE;AACA,OAAO,MAAME,UAAU,gBAAGtB,IAAI,CAG5B,CAAC,EAAE,CAACmB,IAAI,EAAEI,KAAK,KACflB,IAAI,CAACmB,OAAO,CACVC,SAAS,CAACF,KAAK,CAAC,EACfG,MAAM,IAAKC,QAAQ,CAACR,IAAI,EAAGC,KAAK,IAAKlB,OAAO,CAAC0B,GAAG,CAACF,MAAM,EAAEN,KAAK,CAAC,EAAE;EAAES,OAAO,EAAE;AAAI,CAAE,CAAC,CACrF,CAAC;AAEJ;AACA,OAAO,MAAMC,KAAK,GAAGA,CAAA,KAAgCC,YAAY,CAAI,EAAE,CAAC;AAExE;AACA,OAAO,MAAMC,OAAO,gBAAGhC,IAAI,CAGzB,CAAC,EAAE,CAACmB,IAAI,EAAEc,CAAC,KAAKC,SAAS,CAACf,IAAI,EAAE,KAAK,CAAS,EAAE,CAACN,CAAC,EAAEO,KAAK,KAAKa,CAAC,CAACb,KAAK,CAAC,CAAC,CAAC;AAE1E;AACA,OAAO,MAAMW,YAAY,GAAOI,QAAqB,IACnD9B,IAAI,CAAC+B,GAAG,CACN9B,IAAI,CAACyB,YAAY,CAACM,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACC,GAAG,CAAEG,CAAC,IAAgB,CAACA,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EACzEjC,IAAI,IAAK,IAAIQ,QAAQ,CAACR,IAAI,CAAC,CAC7B;AAEH;AACA,OAAO,MAAMsB,GAAG,gBAAG5B,IAAI,CAGrB,CAAC,EAAE,CAACmB,IAAI,EAAEC,KAAK,KAAKd,IAAI,CAACsB,GAAG,CAACT,IAAI,CAACb,IAAI,EAAEc,KAAK,CAAC,CAAC;AAEjD;AACA,OAAO,MAAMoB,YAAY,gBAAGxC,IAAI,CAG9B,CAAC,EAAE,CAACmB,IAAI,EAAEI,KAAK,KACflB,IAAI,CAACmB,OAAO,CACVC,SAAS,CAACF,KAAK,CAAC,EACfG,MAAM,IAAKzB,IAAI,CAACkB,IAAI,EAAEsB,QAAQ,CAAErB,KAAK,IAAKnB,IAAI,CAACyB,MAAM,EAAExB,OAAO,CAAC0B,GAAG,CAACR,KAAK,CAAC,CAAC,EAAE;EAAES,OAAO,EAAE;AAAI,CAAE,CAAC,CAAC,CACjG,CAAC;AAEJ;AACA,OAAO,MAAMa,OAAO,GAAOvB,IAAkB,IAAuBb,IAAI,CAACoC,OAAO,CAACvB,IAAI,CAACb,IAAI,CAAC;AAE3F;AACA,OAAO,MAAMqC,IAAI,GAAGA,CAClB,GAAGC,QAAkB,KACoBb,YAAY,CAACa,QAAQ,CAAC;AAEjE;AACA,OAAO,MAAMC,MAAM,gBAAG7C,IAAI,CAGxB,CAAC,EAAE,CAACmB,IAAI,EAAE2B,IAAI,EAAEb,CAAC,KACjB3B,IAAI,CAACuC,MAAM,CACT1B,IAAI,CAACb,IAAI,EACTwC,IAAI,EACJ,CAACC,GAAG,EAAElC,CAAC,EAAEmC,GAAG,KAAKf,CAAC,CAACc,GAAG,EAAEC,GAAG,CAAC,CAC7B,CAAC;AAEJ;AACA,OAAO,MAAMd,SAAS,gBAAGlC,IAAI,CAG3B,CAAC,EAAE,CAACmB,IAAI,EAAE2B,IAAI,EAAEb,CAAC,KACjB3B,IAAI,CAAC4B,SAAS,CACZf,IAAI,CAACb,IAAI,EACTwC,IAAI,EACJ,CAACC,GAAG,EAAElC,CAAC,EAAEmC,GAAG,KAAKf,CAAC,CAACc,GAAG,EAAEC,GAAG,CAAC,CAC7B,CAAC;AAEJ;AACA,OAAO,MAAMC,MAAM,gBAAGjD,IAAI,CAGxB,CAAC,EAAE,CAACmB,IAAI,EAAEC,KAAK,KAAKd,IAAI,CAAC2C,MAAM,CAAC9B,IAAI,CAACb,IAAI,EAAEc,KAAK,CAAC,CAAC;AAEpD;AACA,OAAO,MAAM8B,SAAS,gBAAGlD,IAAI,CAG3B,CAAC,EAAE,CAACmB,IAAI,EAAEgB,QAAQ,KAAK7B,IAAI,CAAC4C,SAAS,CAAC/B,IAAI,CAACb,IAAI,EAAE6B,QAAQ,CAAC,CAAC;AAE7D;AACA,OAAO,MAAMR,QAAQ,gBAoBjB3B,IAAI,CACLmD,IAAI,IAAKnC,MAAM,CAACmC,IAAI,CAAC,CAAC,CAAC,CAAC,EACzB,CAAChC,IAAI,EAAEiC,SAAS,EAAEC,OAAO,KACvBA,OAAO,EAAExB,OAAO,KAAK,IAAI,GAAGvB,IAAI,CAACqB,QAAQ,CAACR,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKI,SAAS,CAACJ,GAAG,CAAC,EAAE;EAAEnB,OAAO,EAAE;AAAI,CAAE,CAAC,GAAG5B,IAAI,CACrGK,IAAI,CAACqB,QAAQ,CAACR,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKI,SAAS,CAACJ,GAAG,CAAC,CAAC,EACjD3C,IAAI,CAAC+B,GAAG,CAACtC,EAAE,CAACsC,GAAG,CAAEkB,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACtC,CACJ;AAED;AACA,OAAO,MAAMb,QAAQ,gBAoBjBzC,IAAI,CAAEmD,IAAI,IAAKnC,MAAM,CAACmC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAChC,IAAI,EAAEiC,SAAS,EAAEC,OAAO,KAC3DA,OAAO,EAAExB,OAAO,KAAK,IAAI,GACvBvB,IAAI,CAACmC,QAAQ,CAACtB,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKI,SAAS,CAACJ,GAAG,CAAC,EAAE;EAAEnB,OAAO,EAAE;AAAI,CAAE,CAAC,GACpE5B,IAAI,CACFK,IAAI,CAACmC,QAAQ,CAACtB,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKI,SAAS,CAACJ,GAAG,CAAC,CAAC,EACjD3C,IAAI,CAAC+B,GAAG,CAACtC,EAAE,CAACsC,GAAG,CAAEkB,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;AAEN;AACA,OAAO,MAAMC,IAAI,GAAOpC,IAAkB,IAAsBd,IAAI,CAAC+B,GAAG,CAACoB,OAAO,CAACrC,IAAI,CAAC,EAAGsC,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;AAEhH;AACA,OAAO,MAAMC,SAAS,gBAAG3D,IAAI,CAG3B,CAAC,EAAE,CAACmB,IAAI,EAAEyC,EAAE,KAAKtD,IAAI,CAACqD,SAAS,CAACxC,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKY,EAAE,CAACZ,GAAG,CAAC,CAAC,CAAC;AAE/D;AACA,OAAO,MAAMa,YAAY,gBAAG7D,IAAI,CAG9B,CAAC,EAAE,CAACmB,IAAI,EAAEyC,EAAE,KAAKtD,IAAI,CAACuD,YAAY,CAAC1C,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKY,EAAE,CAACZ,GAAG,CAAC,CAAC,CAAC;AAElE;AACA,OAAO,MAAMc,QAAQ,gBAAG9D,IAAI,CAG1B,CAAC,EAAE,CAACmB,IAAI,EAAEyC,EAAE,KAAKtD,IAAI,CAACwD,QAAQ,CAAC3C,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKY,EAAE,CAACZ,GAAG,CAAC,CAAC,CAAC;AAE9D;AACA,OAAO,MAAMe,WAAW,gBAAG/D,IAAI,CAQ7B,CAAC,EAAE,CAACmB,IAAI,EAAEyC,EAAE,KAAKtD,IAAI,CAACyD,WAAW,CAAC5C,IAAI,CAACb,IAAI,EAAG0C,GAAG,IAAKY,EAAE,CAACZ,GAAG,CAAC,CAAC,CAAC;AAEjE;AACA,OAAO,MAAMQ,OAAO,GAAOrC,IAAkB,IAC3Cb,IAAI,CAAC0D,IAAI,CAAC7C,IAAI,CAACb,IAAI,CAAC,CAACL,IAAI,CAACG,GAAG,CAACgC,GAAG,CAACrC,KAAK,CAACkE,eAAe,CAAC,CAAC;AAE3D;AACA,OAAO,MAAMxC,SAAS,GAAON,IAAkB,IAC7C0B,MAAM,CACJ1B,IAAI,EACJjB,OAAO,CAAC4B,KAAK,EAAK,EAClB,CAACiB,GAAG,EAAE3B,KAAK,KAAKnB,IAAI,CAAC8C,GAAG,EAAE7C,OAAO,CAACgB,GAAG,CAACE,KAAK,CAAC,CAAC,CAC9C;AAEH;AACA,OAAO,MAAM8C,OAAO,GAAO/C,IAAkB,IAC3C0B,MAAM,CACJ1B,IAAI,EACJ,EAAE,EACF,CAAC4B,GAAG,EAAE3B,KAAK,KAAK,CAAC,GAAG2B,GAAG,EAAE3B,KAAK,CAAC,CAChC;AAEH;AACA,OAAO,MAAM+C,aAAa,GAAOhD,IAAkB,IACjDd,IAAI,CAAC+B,GAAG,CAAC8B,OAAO,CAAC/C,IAAI,CAAC,EAAGO,MAAM,IAAK,IAAI0C,GAAG,CAAC1C,MAAM,CAAC,CAAC;AAEtD;AACA,OAAO,MAAM2C,SAAS,gBAAGrE,IAAI,CAG3B,CAAC,EAAE,CAACmB,IAAI,EAAEc,CAAC,KAAK3B,IAAI,CAAC+D,SAAS,CAAClD,IAAI,CAACb,IAAI,EAAE,CAAC0C,GAAG,EAAE5B,KAAK,KAAK,CAACa,CAAC,CAACe,GAAG,CAAC,EAAE5B,KAAK,CAAC,CAAC,CAAC;AAE7E;AACA,OAAO,MAAMkD,YAAY,gBAAGtE,IAAI,CAG9B,CAAC,EAAE,CAACmB,IAAI,EAAEc,CAAC,KACX3B,IAAI,CAACgE,YAAY,CACfnD,IAAI,CAACb,IAAI,EACT,CAAC0C,GAAG,EAAE5B,KAAK,KAAKf,IAAI,CAAC+B,GAAG,CAACH,CAAC,CAACe,GAAG,CAAC,EAAGT,CAAC,IAAK,CAACA,CAAC,EAAEnB,KAAK,CAAC,CAAC,CACpD,CAAC;AAEJ;AACA,OAAO,MAAMmD,KAAK,gBAAGvE,IAAI,CAGvB,CAAC,EAAE,CAACmB,IAAI,EAAEI,KAAK,KAAKS,OAAO,CAACT,KAAK,EAAGH,KAAK,IAAKF,GAAG,CAACC,IAAI,EAAEC,KAAK,CAAC,CAAC,CAAC", "ignoreList": []}