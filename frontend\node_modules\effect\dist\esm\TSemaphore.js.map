{"version": 3, "file": "TSemaphore.js", "names": ["internal", "TSemaphoreTypeId", "acquire", "acquireN", "available", "make", "release", "releaseN", "with<PERSON><PERSON><PERSON>", "withPermits", "withPermitScoped", "withPermitsScoped", "unsafeMake", "unsafeMakeSemaphore"], "sources": ["../../src/TSemaphore.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAKA,OAAO,KAAKA,QAAQ,MAAM,8BAA8B;AAKxD;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAkBD,QAAQ,CAACC,gBAAgB;AAmCxE;;;;AAIA,OAAO,MAAMC,OAAO,GAAwCF,QAAQ,CAACE,OAAO;AAE5E;;;;AAIA,OAAO,MAAMC,QAAQ,GAWjBH,QAAQ,CAACG,QAAQ;AAErB;;;;AAIA,OAAO,MAAMC,SAAS,GAA0CJ,QAAQ,CAACI,SAAS;AAElF;;;;AAIA,OAAO,MAAMC,IAAI,GAA6CL,QAAQ,CAACK,IAAI;AAE3E;;;;AAIA,OAAO,MAAMC,OAAO,GAAwCN,QAAQ,CAACM,OAAO;AAE5E;;;;AAIA,OAAO,MAAMC,QAAQ,GAWjBP,QAAQ,CAACO,QAAQ;AAErB;;;;AAIA,OAAO,MAAMC,UAAU,GAWnBR,QAAQ,CAACQ,UAAU;AAEvB;;;;AAIA,OAAO,MAAMC,WAAW,GAWpBT,QAAQ,CAACS,WAAW;AAExB;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAkEV,QAAQ,CAACU,gBAAgB;AAExH;;;;AAIA,OAAO,MAAMC,iBAAiB,GAW1BX,QAAQ,CAACW,iBAAiB;AAE9B;;;;AAIA,OAAO,MAAMC,UAAU,GAAoCZ,QAAQ,CAACa,mBAAmB", "ignoreList": []}