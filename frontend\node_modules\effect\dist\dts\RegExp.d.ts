/**
 * Tests if a value is a `RegExp`.
 *
 * @example
 * ```ts
 * import * as assert from "node:assert"
 * import { RegExp } from "effect"
 *
 * assert.deepStrictEqual(RegExp.isRegExp(/a/), true)
 * assert.deepStrictEqual(RegExp.isRegExp("a"), false)
 * ```
 *
 * @category guards
 * @since 3.9.0
 */
export declare const isRegExp: (input: unknown) => input is RegExp;
/**
 * Escapes special characters in a regular expression pattern.
 *
 * @example
 * ```ts
 * import * as assert from "node:assert"
 * import { RegExp } from "effect"
 *
 * assert.deepStrictEqual(RegExp.escape("a*b"), "a\\*b")
 * ```
 *
 * @since 2.0.0
 */
export declare const escape: (string: string) => string;
//# sourceMappingURL=RegExp.d.ts.map