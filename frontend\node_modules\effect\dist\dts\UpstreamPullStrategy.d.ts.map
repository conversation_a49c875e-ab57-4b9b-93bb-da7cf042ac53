{"version": 3, "file": "UpstreamPullStrategy.d.ts", "sourceRoot": "", "sources": ["../../src/UpstreamPullStrategy.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,0BAA0B,EAAE,OAAO,MAA4C,CAAA;AAE5F;;;GAGG;AACH,MAAM,MAAM,0BAA0B,GAAG,OAAO,0BAA0B,CAAA;AAE1E;;;GAGG;AACH,MAAM,MAAM,oBAAoB,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAA;AAEhF;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,oBAAoB,CAAC;IAC5C;;;OAGG;IACH,UAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7B,QAAQ,CAAC,CAAC,0BAA0B,CAAC,EAAE;YACrC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC5E,QAAQ,CAAC,IAAI,EAAE,eAAe,CAAA;IAC9B,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACzC;AAED;;;GAGG;AACH,MAAM,WAAW,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnF,QAAQ,CAAC,IAAI,EAAE,sBAAsB,CAAA;IACrC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACzC;AAED;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,oBAAoB,CAAC,CAAC,CAA0B,CAAA;AAEpH;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,CAAC,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,oBAAoB,CAAC,CAAC,CAClE,CAAA;AAE/B;;;;;;GAMG;AACH,eAAO,MAAM,sBAAsB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,oBAAoB,CAAC,OAAO,CACrD,CAAA;AAEjC;;;;;;GAMG;AACH,eAAO,MAAM,eAAe,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,aAAa,CAAC,CAAC,CAA4B,CAAA;AAEvH;;;;;;GAMG;AACH,eAAO,MAAM,sBAAsB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,oBAAoB,CAAC,CAAC,CACxE,CAAA;AAEjC;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EACJ,OAAO,EAAE;QACP,QAAQ,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACvD,QAAQ,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;KAC/D,GACC,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACvC;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EACJ,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,EAC7B,OAAO,EAAE;QACP,QAAQ,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACvD,QAAQ,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;KAC/D,GACC,CAAC,CAAA;CACY,CAAA"}