{"version": 3, "file": "upstreamPullRequest.js", "names": ["dual", "hasProperty", "OpCodes", "UpstreamPullRequestSymbolKey", "UpstreamPullRequestTypeId", "Symbol", "for", "upstreamPullRequestVariance", "_A", "_", "proto", "Pulled", "value", "op", "Object", "create", "_tag", "OP_PULLED", "NoUpstream", "activeDownstreamCount", "OP_NO_UPSTREAM", "isUpstreamPullRequest", "u", "isPulled", "self", "isNoUpstream", "match", "onNoUpstream", "onPulled"], "sources": ["../../../../src/internal/channel/upstreamPullRequest.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,IAAI,QAAQ,mBAAmB;AACxC,SAASC,WAAW,QAAQ,oBAAoB;AAEhD,OAAO,KAAKC,OAAO,MAAM,0CAA0C;AAEnE;AACA,MAAMC,4BAA4B,GAAG,mCAAmC;AAExE;AACA,OAAO,MAAMC,yBAAyB,gBAAkDC,MAAM,CAACC,GAAG,CAChGH,4BAA4B,CACoB;AAElD,MAAMI,2BAA2B,GAAG;EAClC;EACAC,EAAE,EAAGC,CAAQ,IAAKA;CACnB;AAED;AACA,MAAMC,KAAK,GAAG;EACZ,CAACN,yBAAyB,GAAGG;CAC9B;AAED;AACA,OAAO,MAAMI,MAAM,GAAOC,KAAQ,IAAgD;EAChF,MAAMC,EAAE,GAAGC,MAAM,CAACC,MAAM,CAACL,KAAK,CAAC;EAC/BG,EAAE,CAACG,IAAI,GAAGd,OAAO,CAACe,SAAS;EAC3BJ,EAAE,CAACD,KAAK,GAAGA,KAAK;EAChB,OAAOC,EAAE;AACX,CAAC;AAED;AACA,OAAO,MAAMK,UAAU,GAAIC,qBAA6B,IAAoD;EAC1G,MAAMN,EAAE,GAAGC,MAAM,CAACC,MAAM,CAACL,KAAK,CAAC;EAC/BG,EAAE,CAACG,IAAI,GAAGd,OAAO,CAACkB,cAAc;EAChCP,EAAE,CAACM,qBAAqB,GAAGA,qBAAqB;EAChD,OAAON,EAAE;AACX,CAAC;AAED;AACA,OAAO,MAAMQ,qBAAqB,GAAIC,CAAU,IAC9CrB,WAAW,CAACqB,CAAC,EAAElB,yBAAyB,CAAC;AAE3C;AACA,OAAO,MAAMmB,QAAQ,GACnBC,IAAgD,IACNA,IAAI,CAACR,IAAI,KAAKd,OAAO,CAACe,SAAS;AAE3E;AACA,OAAO,MAAMQ,YAAY,GACvBD,IAAgD,IACLA,IAAI,CAACR,IAAI,KAAKd,OAAO,CAACkB,cAAc;AAEjF;AACA,OAAO,MAAMM,KAAK,gBAAG1B,IAAI,CAcvB,CAAC,EAAE,CACHwB,IAAgD,EAChD;EAAEG,YAAY;EAAEC;AAAQ,CAGvB,KACI;EACL,QAAQJ,IAAI,CAACR,IAAI;IACf,KAAKd,OAAO,CAACe,SAAS;MAAE;QACtB,OAAOW,QAAQ,CAACJ,IAAI,CAACZ,KAAK,CAAC;MAC7B;IACA,KAAKV,OAAO,CAACkB,cAAc;MAAE;QAC3B,OAAOO,YAAY,CAACH,IAAI,CAACL,qBAAqB,CAAC;MACjD;EACF;AACF,CAAC,CAAC", "ignoreList": []}