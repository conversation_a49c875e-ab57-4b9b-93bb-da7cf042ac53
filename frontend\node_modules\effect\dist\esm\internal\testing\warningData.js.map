{"version": 3, "file": "warningData.js", "names": ["OP_WARNING_DATA_START", "OP_WARNING_DATA_PENDING", "OP_WARNING_DATA_DONE", "start", "_tag", "pending", "fiber", "done", "isStart", "self", "isPending", "isDone"], "sources": ["../../../../src/internal/testing/warningData.ts"], "sourcesContent": [null], "mappings": "AAaA;AACA,OAAO,MAAMA,qBAAqB,GAAG,OAAgB;AAKrD;AACA,OAAO,MAAMC,uBAAuB,GAAG,SAAkB;AAKzD;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAe;AAqBnD;;;;;AAKA,OAAO,MAAMC,KAAK,GAAgB;EAChCC,IAAI,EAAEJ;CACP;AAED;;;;;;;AAOA,OAAO,MAAMK,OAAO,GAAIC,KAA+B,IAAiB;EACtE,OAAO;IACLF,IAAI,EAAEH,uBAAuB;IAC7BK;GACD;AACH,CAAC;AAED;;;;;;AAMA,OAAO,MAAMC,IAAI,GAAgB;EAC/BH,IAAI,EAAEF;CACP;AAED;AACA,OAAO,MAAMM,OAAO,GAAIC,IAAiB,IAAmB;EAC1D,OAAOA,IAAI,CAACL,IAAI,KAAKJ,qBAAqB;AAC5C,CAAC;AAED;AACA,OAAO,MAAMU,SAAS,GAAID,IAAiB,IAAqB;EAC9D,OAAOA,IAAI,CAACL,IAAI,KAAKH,uBAAuB;AAC9C,CAAC;AAED;AACA,OAAO,MAAMU,MAAM,GAAIF,IAAiB,IAAkB;EACxD,OAAOA,IAAI,CAACL,IAAI,KAAKF,oBAAoB;AAC3C,CAAC", "ignoreList": []}