/**
 * @since 2.0.0
 * @category models
 */
export interface LogSpan {
    readonly label: string;
    readonly startTime: number;
}
/**
 * @since 2.0.0
 * @category constructors
 */
export declare const make: (label: string, startTime: number) => LogSpan;
/**
 * @since 2.0.0
 * @category destructors
 */
export declare const render: (now: number) => (self: LogSpan) => string;
//# sourceMappingURL=LogSpan.d.ts.map