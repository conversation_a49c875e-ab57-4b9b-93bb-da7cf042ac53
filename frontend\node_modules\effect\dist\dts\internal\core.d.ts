import type * as Effect from "../Effect.js";
import * as FiberId from "../FiberId.js";
declare const async_: <A, E = never, R = never>(resume: (callback: (_: Effect.Effect<A, E, R>) => void, signal: AbortSignal) => void | Effect.Effect<void, never, R>, blockingOn?: FiberId.FiberId) => Effect.Effect<A, E, R>;
export { 
/** @internal */
async_ as async };
declare const void_: Effect.Effect<void>;
export { void_ as void };
//# sourceMappingURL=core.d.ts.map