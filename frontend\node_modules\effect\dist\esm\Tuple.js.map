{"version": 3, "file": "Tuple.js", "names": ["Equivalence", "dual", "order", "make", "elements", "get<PERSON><PERSON><PERSON>", "self", "getSecond", "map", "fn", "element", "mapBoth", "onFirst", "onSecond", "mapFirst", "f", "mapSecond", "swap", "getEquivalence", "tuple", "getOrder", "appendElement", "that", "at", "index", "isTupleOf", "isTupleOfAtLeast"], "sources": ["../../src/Tuple.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;;AAKA,OAAO,KAAKA,WAAW,MAAM,kBAAkB;AAC/C,SAASC,IAAI,QAAQ,eAAe;AAEpC,OAAO,KAAKC,KAAK,MAAM,YAAY;AAWnC;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,IAAI,GAAGA,CAA+B,GAAGC,QAAW,KAAQA,QAAQ;AAEjF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,QAAQ,GAAUC,IAAqB,IAAQA,IAAI,CAAC,CAAC,CAAC;AAEnE;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,SAAS,GAAUD,IAAqB,IAAQA,IAAI,CAAC,CAAC,CAAC;AAEpE;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAME,GAAG,gBAuCZP,IAAI,CACN,CAAC,EACD,CACEK,IAAmB,EACnBG,EAAqB,KACHH,IAAI,CAACE,GAAG,CAAEE,OAAO,IAAKD,EAAE,CAACC,OAAO,CAAC,CAAkB,CACxE;AAED;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,OAAO,gBAgDhBV,IAAI,CACN,CAAC,EACD,CACEK,IAAuB,EACvB;EAAEM,OAAO;EAAEC;AAAQ,CAGlB,KACY,CAACD,OAAO,CAACN,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEO,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACrD;AAED;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMQ,QAAQ,gBAqCjBb,IAAI,CAAC,CAAC,EAAE,CAAYK,IAAsB,EAAES,CAAmB,KAAc,CAACA,CAAC,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAEvG;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMU,SAAS,gBAqClBf,IAAI,CAAC,CAAC,EAAE,CAAYK,IAAsB,EAAES,CAAoB,KAAc,CAACT,IAAI,CAAC,CAAC,CAAC,EAAES,CAAC,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAExG;;;;;;;;;;;;;AAaA,OAAO,MAAMW,IAAI,GAAUX,IAAqB,IAAa,CAACA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;AAE/E;;;;;;;AAOA,OAAO,MAAMY,cAAc,GAIvBlB,WAAW,CAACmB,KAAK;AAErB;;;;;;;;;AASA,OAAO,MAAMC,QAAQ,GAEqElB,KAAK,CAACiB,KAAK;AAErG;;;;;;AAMA,OAAO,MAAME,aAAa,gBAetBpB,IAAI,CAAC,CAAC,EAAE,CAAsCK,IAAO,EAAEgB,IAAO,KAAgB,CAAC,GAAGhB,IAAI,EAAEgB,IAAI,CAAC,CAAC;AAElG;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,EAAE,gBA+BXtB,IAAI,CAAC,CAAC,EAAE,CAAqDK,IAAO,EAAEkB,KAAQ,KAAWlB,IAAI,CAACkB,KAAK,CAAC,CAAC;AAEzG;AACE;;;;;;;;;;;;;;;;;;;;;;;;AAwBAC,SAAS;AACT;;;;;;;;;;;;;;;;;;;;;;;;AAwBAC,gBAAgB,QACX,gBAAgB", "ignoreList": []}