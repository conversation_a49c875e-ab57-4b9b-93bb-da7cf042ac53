/** @internal */
export const OP_WITH_STM_RUNTIME = "WithSTMRuntime" as const

/** @internal */
export type OP_WITH_STM_RUNTIME = typeof OP_WITH_STM_RUNTIME

/** @internal */
export const OP_ON_FAILURE = "OnFailure" as const

/** @internal */
export type OP_ON_FAILURE = typeof OP_ON_FAILURE

/** @internal */
export const OP_ON_RETRY = "OnRetry" as const

/** @internal */
export type OP_ON_RETRY = typeof OP_ON_RETRY

/** @internal */
export const OP_ON_SUCCESS = "OnSuccess" as const

/** @internal */
export type OP_ON_SUCCESS = typeof OP_ON_SUCCESS

/** @internal */
export const OP_PROVIDE = "Provide" as const

/** @internal */
export type OP_PROVIDE = typeof OP_PROVIDE

/** @internal */
export const OP_SYNC = "Sync" as const

/** @internal */
export type OP_SYNC = typeof OP_SYNC

/** @internal */
export const OP_SUCCEED = "Succeed" as const

/** @internal */
export type OP_SUCCEED = typeof OP_SUCCEED

/** @internal */
export const OP_RETRY = "Retry" as const

/** @internal */
export type OP_RETRY = typeof OP_RETRY

/** @internal */
export const OP_FAIL = "Fail" as const

/** @internal */
export type OP_FAIL = typeof OP_FAIL

/** @internal */
export const OP_DIE = "Die" as const

/** @internal */
export type OP_DIE = typeof OP_DIE

/** @internal */
export const OP_INTERRUPT = "Interrupt" as const

/** @internal */
export type OP_INTERRUPT = typeof OP_INTERRUPT

/** @internal */
export const OP_TRACED = "Traced" as const

/** @internal */
export type OP_TRACED = typeof OP_TRACED
