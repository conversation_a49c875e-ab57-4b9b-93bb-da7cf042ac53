{"version": 3, "file": "tSemaphore.js", "names": ["Cause", "Effect", "dual", "STM", "core", "tRef", "TSemaphoreSymbolKey", "TSemaphoreTypeId", "Symbol", "for", "TSemaphoreImpl", "permits", "constructor", "make", "map", "acquire", "self", "acquireN", "n", "withSTMRuntime", "driver", "IllegalArgumentException", "value", "unsafeGet", "journal", "retry", "succeed", "unsafeSet", "available", "get", "release", "releaseN", "current", "with<PERSON><PERSON><PERSON>", "semaphore", "withPermits", "uninterruptibleMask", "restore", "zipRight", "commit", "ensuring", "withPermitScoped", "withPermitsScoped", "acquireReleaseInterruptible", "unsafeMakeSemaphore", "TRefImpl"], "sources": ["../../../../src/internal/stm/tSemaphore.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,SAASC,IAAI,QAAQ,mBAAmB;AAExC,OAAO,KAAKC,GAAG,MAAM,cAAc;AAGnC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,IAAI,MAAM,WAAW;AAEjC;AACA,MAAMC,mBAAmB,GAAG,mBAAmB;AAE/C;AACA,OAAO,MAAMC,gBAAgB,gBAAgCC,MAAM,CAACC,GAAG,CACrEH,mBAAmB,CACW;AAEhC;AACA,MAAMI,cAAc;EAEGC,OAAA;EADZ,CAACJ,gBAAgB,IAAiCA,gBAAgB;EAC3EK,YAAqBD,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;EAAsB;;AAGpD;AACA,OAAO,MAAME,IAAI,GAAIF,OAAe,IAClCR,GAAG,CAACW,GAAG,CAACT,IAAI,CAACQ,IAAI,CAACF,OAAO,CAAC,EAAGA,OAAO,IAAK,IAAID,cAAc,CAACC,OAAO,CAAC,CAAC;AAEvE;AACA,OAAO,MAAMI,OAAO,GAAIC,IAA2B,IAAoBC,QAAQ,CAACD,IAAI,EAAE,CAAC,CAAC;AAExF;AACA,OAAO,MAAMC,QAAQ,gBAAGf,IAAI,CAG1B,CAAC,EAAE,CAACc,IAAI,EAAEE,CAAC,KACXd,IAAI,CAACe,cAAc,CAAEC,MAAM,IAAI;EAC7B,IAAIF,CAAC,GAAG,CAAC,EAAE;IACT,MAAM,IAAIlB,KAAK,CAACqB,wBAAwB,CAAC,6BAA6BH,CAAC,+BAA+B,CAAC;EACzG;EACA,MAAMI,KAAK,GAAGjB,IAAI,CAACkB,SAAS,CAACP,IAAI,CAACL,OAAO,EAAES,MAAM,CAACI,OAAO,CAAC;EAC1D,IAAIF,KAAK,GAAGJ,CAAC,EAAE;IACb,OAAOf,GAAG,CAACsB,KAAK;EAClB,CAAC,MAAM;IACL,OAAOtB,GAAG,CAACuB,OAAO,CAACrB,IAAI,CAACsB,SAAS,CAACX,IAAI,CAACL,OAAO,EAAEW,KAAK,GAAGJ,CAAC,EAAEE,MAAM,CAACI,OAAO,CAAC,CAAC;EAC7E;AACF,CAAC,CAAC,CAAC;AAEL;AACA,OAAO,MAAMI,SAAS,GAAIZ,IAA2B,IAAKX,IAAI,CAACwB,GAAG,CAACb,IAAI,CAACL,OAAO,CAAC;AAEhF;AACA,OAAO,MAAMmB,OAAO,GAAId,IAA2B,IAAoBe,QAAQ,CAACf,IAAI,EAAE,CAAC,CAAC;AAExF;AACA,OAAO,MAAMe,QAAQ,gBAAG7B,IAAI,CAG1B,CAAC,EAAE,CAACc,IAAI,EAAEE,CAAC,KACXd,IAAI,CAACe,cAAc,CAAEC,MAAM,IAAI;EAC7B,IAAIF,CAAC,GAAG,CAAC,EAAE;IACT,MAAM,IAAIlB,KAAK,CAACqB,wBAAwB,CAAC,6BAA6BH,CAAC,+BAA+B,CAAC;EACzG;EACA,MAAMc,OAAO,GAAG3B,IAAI,CAACkB,SAAS,CAACP,IAAI,CAACL,OAAO,EAAES,MAAM,CAACI,OAAO,CAAC;EAC5D,OAAOrB,GAAG,CAACuB,OAAO,CAACrB,IAAI,CAACsB,SAAS,CAACX,IAAI,CAACL,OAAO,EAAEqB,OAAO,GAAGd,CAAC,EAAEE,MAAM,CAACI,OAAO,CAAC,CAAC;AAC/E,CAAC,CAAC,CAAC;AAEL;AACA,OAAO,MAAMS,UAAU,gBAAG/B,IAAI,CAG5B,CAAC,EAAE,CAACc,IAAI,EAAEkB,SAAS,KAAKC,WAAW,CAACnB,IAAI,EAAEkB,SAAS,EAAE,CAAC,CAAC,CAAC;AAE1D;AACA,OAAO,MAAMC,WAAW,gBAAGjC,IAAI,CAU7B,CAAC,EAAE,CAACc,IAAI,EAAEkB,SAAS,EAAEvB,OAAO,KAC5BV,MAAM,CAACmC,mBAAmB,CAAEC,OAAO,IACjCpC,MAAM,CAACqC,QAAQ,CACbD,OAAO,CAACjC,IAAI,CAACmC,MAAM,CAACtB,QAAQ,CAACN,OAAO,CAAC,CAACuB,SAAS,CAAC,CAAC,CAAC,EAClDjC,MAAM,CAACuC,QAAQ,CACbxB,IAAI,EACJZ,IAAI,CAACmC,MAAM,CAACR,QAAQ,CAACpB,OAAO,CAAC,CAACuB,SAAS,CAAC,CAAC,CAC1C,CACF,CACF,CAAC;AAEJ;AACA,OAAO,MAAMO,gBAAgB,GAAIzB,IAA2B,IAC1D0B,iBAAiB,CAAC1B,IAAI,EAAE,CAAC,CAAC;AAE5B;AACA,OAAO,MAAM0B,iBAAiB,gBAAGxC,IAAI,CAGnC,CAAC,EAAE,CAACc,IAAI,EAAEL,OAAO,KACjBV,MAAM,CAAC0C,2BAA2B,CAChCvC,IAAI,CAACmC,MAAM,CAACtB,QAAQ,CAACD,IAAI,EAAEL,OAAO,CAAC,CAAC,EACpC,MAAMP,IAAI,CAACmC,MAAM,CAACR,QAAQ,CAACf,IAAI,EAAEL,OAAO,CAAC,CAAC,CAC3C,CAAC;AAEJ;AACA,OAAO,MAAMiC,mBAAmB,GAAIjC,OAAe,IAA2B;EAC5E,OAAO,IAAID,cAAc,CAAC,IAAIL,IAAI,CAACwC,QAAQ,CAAClC,OAAO,CAAC,CAAC;AACvD,CAAC", "ignoreList": []}