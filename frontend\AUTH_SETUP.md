# NextAuth.js Authentication Setup

This document provides instructions for setting up and configuring the NextAuth.js authentication system that has been implemented in this project.

## Features Implemented

✅ **Email/Password Authentication** - Users can register and sign in with email and password  
✅ **Google OAuth Provider** - Social login with Google accounts  
✅ **Facebook OAuth Provider** - Social login with Facebook accounts  
✅ **Database Integration** - Configured with Prisma adapter for database storage  
✅ **Server Actions** - All authentication logic uses Next.js Server Actions  
✅ **Route Protection** - Middleware protects authenticated routes  
✅ **Session Management** - Proper session handling with JWT strategy  
✅ **TypeScript Support** - Full TypeScript types and interfaces  

## Setup Instructions

### 1. Environment Variables

Copy the `.env.local` file and update the following variables:

```bash
# Generate a secure secret (you can use: openssl rand -base64 32)
NEXTAUTH_SECRET=your-secure-secret-here

# Your application URL
NEXTAUTH_URL=http://localhost:3000

# Database connection (will be provided by backend team)
DATABASE_URL="postgresql://username:password@localhost:5432/mrh_platform?schema=public"

# Google OAuth (get from Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth (get from Facebook Developers)
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
```

### 2. OAuth Provider Setup

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`
6. Copy Client ID and Client Secret to `.env.local`

#### Facebook OAuth Setup
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or select existing one
3. Add "Facebook Login" product
4. Set Valid OAuth Redirect URIs: `http://localhost:3000/api/auth/callback/facebook`
5. Copy App ID and App Secret to `.env.local`

### 3. Database Setup

Once the backend database is available:

1. Update the `DATABASE_URL` in `.env.local`
2. Run Prisma migrations:
   ```bash
   npx prisma migrate dev --name init
   ```
3. Generate Prisma client:
   ```bash
   npx prisma generate
   ```

### 4. Testing the Implementation

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:3000`

3. Test the authentication flow:
   - Click "Sign Up" to create a new account
   - Click "Sign In" to log in
   - Try OAuth providers (Google/Facebook)
   - Access protected route: `/dashboard`

## File Structure

```
src/
├── app/
│   ├── (auth)/
│   │   ├── signin/page.tsx          # Sign in page
│   │   └── signup/page.tsx          # Sign up page
│   ├── (dashboard)/
│   │   └── dashboard/page.tsx       # Protected dashboard
│   ├── api/auth/[...nextauth]/
│   │   └── route.ts                 # NextAuth API routes
│   └── layout.tsx                   # Root layout with SessionProvider
├── components/
│   ├── auth/
│   │   ├── SignInForm.tsx           # Sign in form component
│   │   └── SignUpForm.tsx           # Sign up form component
│   └── providers/
│       └── SessionProvider.tsx      # Session provider wrapper
├── lib/
│   ├── auth-actions.ts              # Server actions for auth
│   └── auth-utils.ts                # Authentication utilities
├── types/
│   └── auth.ts                      # TypeScript type definitions
├── auth.ts                          # NextAuth configuration
├── middleware.ts                    # Route protection middleware
└── prisma/
    └── schema.prisma                # Database schema
```

## Key Components

### Server Actions
- `signInWithCredentials()` - Email/password sign in
- `signUpWithCredentials()` - User registration
- `signInWithGoogle()` - Google OAuth sign in
- `signInWithFacebook()` - Facebook OAuth sign in
- `signOutAction()` - Sign out

### Utilities
- `getSession()` - Get current session
- `getCurrentUser()` - Get current user
- `requireAuth()` - Require authentication (redirect if not)
- `isAuthenticated()` - Check if user is authenticated

### Route Protection
The middleware automatically:
- Redirects unauthenticated users from `/dashboard` to `/auth/signin`
- Redirects authenticated users from auth pages to `/dashboard`

## Notes

- The implementation uses NextAuth.js v5 (Auth.js)
- JWT strategy is used for sessions
- Prisma is configured as the database adapter
- All forms use React Server Actions for better performance
- The implementation follows Next.js 13+ App Router conventions

## Troubleshooting

1. **Database connection issues**: Ensure DATABASE_URL is correct and database is running
2. **OAuth not working**: Check redirect URIs match exactly in provider settings
3. **Session issues**: Verify NEXTAUTH_SECRET is set and consistent
4. **TypeScript errors**: Run `npx prisma generate` to update types
