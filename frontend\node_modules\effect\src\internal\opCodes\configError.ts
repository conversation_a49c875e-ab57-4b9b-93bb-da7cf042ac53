/** @internal */
export type OP_AND = typeof OP_AND

/** @internal */
export const OP_AND = "And" as const

/** @internal */
export type OP_OR = typeof OP_OR

/** @internal */
export const OP_OR = "Or" as const

/** @internal */
export type OP_INVALID_DATA = typeof OP_INVALID_DATA

/** @internal */
export const OP_INVALID_DATA = "InvalidData" as const

/** @internal */
export type OP_MISSING_DATA = typeof OP_MISSING_DATA

/** @internal */
export const OP_MISSING_DATA = "MissingData" as const

/** @internal */
export type OP_SOURCE_UNAVAILABLE = typeof OP_SOURCE_UNAVAILABLE

/** @internal */
export const OP_SOURCE_UNAVAILABLE = "SourceUnavailable" as const

/** @internal */
export type OP_UNSUPPORTED = typeof OP_UNSUPPORTED

/** @internal */
export const OP_UNSUPPORTED = "Unsupported" as const
